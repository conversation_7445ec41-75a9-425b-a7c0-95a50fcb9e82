#!/bin/bash
# =================================================================
# Shell script to upscale an image with two different methods,
# blend the results, and apply a final post-processing pass.
#
# Dependencies:
# 1. ImageMagick (the 'magick' command must be in your PATH)
# 2. realesrgan-ncnn-vulkan (must be in the same folder or in your PATH)
#
# Usage:
# 1. Save this file as "process_images.sh".
# 2. Make it executable: chmod +x process_images.sh
# 3. Run the script from your terminal providing an image file as an argument:
#    ./process_images.sh my_image.png
# =================================================================

# --- Configuration ---
# Check if an input file was provided on the command line
if [ -z "$1" ]; then
    echo "ERROR: No input file specified."
    echo "Usage: $0 <input_image_file>"
    exit 1
fi

INPUT_FILE="$1"
BASENAME="${INPUT_FILE%.*}" # Get the filename without its extension

# Define output filenames based on the input file's name
MAGICK_OUTPUT="${BASENAME}_magick.png"
REALESRGAN_TEMP="${BASENAME}_realesrgan_temp.png"
REALESRGAN_OUTPUT="${BASENAME}_realesrgan.png"
FINAL_OUTPUT="${BASENAME}_final.png"
FINAL_POST_OUTPUT="${BASENAME}_final_post.png"
COMBINED_OUTPUT="${BASENAME}_combined.png"
TARGET_RESOLUTION="928x1232"

# --- Blending Percentage ---
# Set the percentage of the Real-ESRGAN result to blend into the final image.
# A value of 50 means a 50/50 blend.
REALESRGAN_BLEND_PERCENT=25

# --- Post-Processing Levels ---
# Set the parameters for the final image touch-up.
POST_NOISE_TYPE="Uniform"          # Type of noise to add (e.g., Gaussian, Uniform)
POST_NOISE_ATTENUATE="0.2"          # Reduces noise amplitude. 1.0 is full, 0.2 is subtle.
POST_BLUR_SIGMA="0x0.5"             # Blur amount (radius x sigma)
POST_UNSHARP_PARAMS="0x0.75+0.75+0.02" # Unsharp mask (radius x sigma + amount + threshold)

# --- Pre-flight Checks ---
echo "Checking for required files and programs..."

if [ ! -f "$INPUT_FILE" ]; then
    echo "ERROR: Input file '$INPUT_FILE' not found!"
    exit 1
fi

if ! command -v magick &> /dev/null; then
    echo "ERROR: ImageMagick ('magick' command) not found in your system's PATH."
    exit 1
fi

if ! command -v realesrgan-ncnn-vulkan &> /dev/null; then
    echo "ERROR: 'realesrgan-ncnn-vulkan' not found in your system's PATH."
    exit 1
fi

echo "All checks passed. Starting process for '$INPUT_FILE'..."
echo

# --- Step 1: Upscale with ImageMagick and apply unsharp mask ---
echo "[1/6] Upscaling with ImageMagick to $TARGET_RESOLUTION..."
magick "$INPUT_FILE" -resize ${TARGET_RESOLUTION}\! -filter Lanczos -unsharp 1x0.5+1+0.05 "$MAGICK_OUTPUT"
echo "     ...ImageMagick upscale complete. Saved as $MAGICK_OUTPUT"
echo

# --- Step 2: Upscale with Real-ESRGAN ---
echo "[2/6] Upscaling with Real-ESRGAN (this may take a moment)..."
realesrgan-ncnn-vulkan -i "$INPUT_FILE" -o "$REALESRGAN_TEMP" -n realesrgan-x4plus
echo "     ...Real-ESRGAN upscale complete."
echo

# --- Step 3: Resize Real-ESRGAN output to match target resolution ---
echo "[3/6] Resizing Real-ESRGAN output to $TARGET_RESOLUTION..."
magick "$REALESRGAN_TEMP" -resize ${TARGET_RESOLUTION}\! "$REALESRGAN_OUTPUT"
echo "     ...Resizing complete. Saved as $REALESRGAN_OUTPUT"
echo

# --- Step 4: Blend the two upscaled images ---
IMAGEMAGICK_BLEND_PERCENT=$((100 - REALESRGAN_BLEND_PERCENT))
echo "[4/6] Blending images (${REALESRGAN_BLEND_PERCENT}% Real-ESRGAN, ${IMAGEMAGICK_BLEND_PERCENT}% ImageMagick)..."
magick "$MAGICK_OUTPUT" "$REALESRGAN_OUTPUT" -compose dissolve -define compose:args=${REALESRGAN_BLEND_PERCENT} -composite "$FINAL_OUTPUT"
echo "     ...Blending complete. Saved as $FINAL_OUTPUT"
echo

# --- Step 5: Apply post-processing to the final blended image ---
echo "[5/6] Applying post-processing (noise, blur, sharpen)..."
magick "$FINAL_OUTPUT" +noise "$POST_NOISE_TYPE" -attenuate "$POST_NOISE_ATTENUATE" -blur "$POST_BLUR_SIGMA" -unsharp "$POST_UNSHARP_PARAMS" "$FINAL_POST_OUTPUT"
echo "     ...Post-processing complete."
echo

# --- Step 6: Create side-by-side comparison image ---
echo "[6/6] Creating side-by-side comparison image..."

# Prepare list of images and labels for comparison
COMPARISON_IMAGES=()
COMPARISON_LABELS=()

# Check if original file exists and add it
if [ -f "${BASENAME}_orig.png" ]; then
    COMPARISON_IMAGES+=("${BASENAME}_orig.png")
    COMPARISON_LABELS+=("Original")
fi

# Add the processed images
COMPARISON_IMAGES+=("$REALESRGAN_OUTPUT")
COMPARISON_LABELS+=("Real-ESRGAN")

COMPARISON_IMAGES+=("$MAGICK_OUTPUT")
COMPARISON_LABELS+=("ImageMagick")

COMPARISON_IMAGES+=("$FINAL_OUTPUT")
COMPARISON_LABELS+=("Blended")

COMPARISON_IMAGES+=("$FINAL_POST_OUTPUT")
COMPARISON_LABELS+=("Final")

# Create labeled versions of each image
LABELED_IMAGES=()
for i in "${!COMPARISON_IMAGES[@]}"; do
    IMAGE="${COMPARISON_IMAGES[$i]}"
    LABEL="${COMPARISON_LABELS[$i]}"
    LABELED_IMAGE="${BASENAME}_labeled_${i}.png"

    # Check if the source image exists
    if [ ! -f "$IMAGE" ]; then
        echo "     Warning: Image $IMAGE not found, skipping..."
        continue
    fi

    # Add label to the bottom of each image (using default font instead of Arial)
    magick "$IMAGE" \( -background white -fill black -pointsize 24 \
           -gravity center label:"$LABEL" \) -gravity south -append "$LABELED_IMAGE"

    # Check if labeled image was created successfully
    if [ -f "$LABELED_IMAGE" ]; then
        LABELED_IMAGES+=("$LABELED_IMAGE")
    else
        echo "     Warning: Failed to create labeled image for $LABEL"
    fi
done

# Combine all labeled images side by side
magick "${LABELED_IMAGES[@]}" +append "$COMBINED_OUTPUT"

# Clean up temporary labeled images
for labeled_img in "${LABELED_IMAGES[@]}"; do
    rm "$labeled_img"
done

echo "     ...Comparison image created. Saved as $COMBINED_OUTPUT"
echo

# --- Cleanup ---
echo "Cleaning up temporary files..."
rm "$REALESRGAN_TEMP"

echo
echo "================================================="
echo "     Process finished!"
echo "     Final post-processed image saved as: $FINAL_POST_OUTPUT"
echo "     Side-by-side comparison saved as: $COMBINED_OUTPUT"
echo "================================================="
echo
